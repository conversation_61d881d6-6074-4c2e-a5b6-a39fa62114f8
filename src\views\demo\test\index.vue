<template>
    <div class="document-editor-wrapper">
        <div class="document-editor-container">
            <DocumentEditor
                v-if="isComponentReady"
                id="docEditor"
                document-server-url="http://************:9066/"
                :config="config"
                :events="config.events"
                :on-load-component-error="onLoadComponentError"
            />
            <div v-else class="loading">
                正在加载文档编辑器...
                <!-- 9066自动化，9070社区版 -->
            </div>
        </div>
        <div class="suggestion-panel"> 
            <div class="suggestion-list">
                <a-button type="primary" @click="testJumpPage(10)">跳转页码</a-button>
                <a-button type="primary" @click="testHighlight(
                    {
                    page:2,position:[{
                    page: 3,
                    x1: 77, y1: 249, x2: 536, y2: 279
                }]})">跳转2页，第3页有高亮选区</a-button>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, nextTick } from 'vue';
import { DocumentEditor } from '@onlyoffice/document-editor-vue';
import { message } from 'ant-design-vue';
import CryptoJS from 'crypto-js'; 
const isComponentReady = ref(false);
const docEditorInstance = ref<any>(null);
const connector = ref<any>(null);
// onlyoffice配置
const config = reactive({
    document: {
        fileType: 'docx',
        key: 'demo_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9),
        title: '测试文件.docx',
        // url: 'https://sppgpttest.gcycloud.cn/sftp/file/test/default/20250721/1947172506146705408/测试文件.docx',
        url:'http://************:8090/files/test.docx',
        permissions: {
            edit: true,
            download: true,
            print: true,
            review: true,
            comment: true
        }
    },
    documentType: 'word',
    token: '' as string, // JWT token
    editorConfig: {
        mode: 'edit',
        lang: 'zh-CN',
        customization: {
            autosave: false,
            forcesave: true,
            features: {
                spellcheck: false,
                ruler: true,
                pageNavigation: false,
                leftMenu: false,
                rightMenu: false,
                header: true,
                footer: true
            }
        },
        user: {
            id: 'user_' + Date.now(),
            name: '测试用户'
        },
        // 添加回调URL
        callbackUrl: "http://************:18000/callback"
    },
    events: {
        onAppReady: (event: any) => {
            console.log('OnlyOffice 应用已准备就绪', event); 
        },
        onDocumentReady: (event: any) => {
            console.log('文档加载成功', event);

            if (event && event.target) {
                // 保存编辑器实例
                docEditorInstance.value = event.target;
                (window as any).docEditor = event.target;

                console.log('编辑器实例已保存:', event.target);
                console.log('可用方法列表:', Object.getOwnPropertyNames(event.target));

                // 检查可用的方法
                const availableMethods = [
                    'showMessage', 'insertImage', 'downloadAs', 'refreshHistory',
                    'setHistoryData', 'denyEditingRights', 'requestEditRights'
                ];

                console.log('=== 可用方法检查 ===');
                availableMethods.forEach(method => {
                    const isAvailable = typeof event.target[method] === 'function';
                    console.log(`${method}: ${isAvailable ? '✅' : '❌'}`);
                });

                // 由于 createConnector 不可用，我们使用其他方法
                console.log('⚠️ createConnector 不可用，使用替代方案');
                console.log('✅ 编辑器实例已准备就绪，可以使用基础 API');
            }
        },
        onError: (event: any) => {
            console.error('OnlyOffice错误:', event);
        },
        onInfo: (event: any) => {
            // console.log('OnlyOffice信息:', event);
        }
    }
}); 

const onLoadComponentError = (errorCode: number, errorDescription: string) => {
    console.error('OnlyOffice错误:', errorCode, errorDescription);
    
    if (errorCode === -2) {
        message.error('文档服务器连接失败，请检查服务器地址');
    } else if (errorCode === -3) {
        message.error('文档安全令牌错误，请联系管理员');
    } else {
        message.error(`文档编辑器错误: ${errorDescription}`);
    }
};
// 测试页码跳转功能 - 不依赖连接器的实现
const testJumpPage = async (page: number) => {
    console.log(`=== 跳转到第${page}页 ===`);

    if (!docEditorInstance.value) {
        console.error('❌ 编辑器实例不可用');
        message.error('编辑器未准备就绪，请等待文档加载完成');
        return;
    }

    console.log('✅ 编辑器实例可用，开始测试...');
    console.log('编辑器实例:', docEditorInstance.value);

    try {
        // 方法1: 使用 showMessage（如果可用）
        if (typeof docEditorInstance.value.showMessage === 'function') {
            const message = `正在跳转到第${page}页...`;
            docEditorInstance.value.showMessage(message);
            console.log('✅ showMessage 调用成功:', message);
        } else {
            console.log('❌ showMessage 方法不可用');
        }

        // 方法2: 尝试使用 insertImage 来测试 API 可用性
        if (typeof docEditorInstance.value.insertImage === 'function') {
            console.log('✅ insertImage 方法可用');
        } else {
            console.log('❌ insertImage 方法不可用');
        }

        // 方法3: 尝试使用 downloadAs 来测试 API 可用性
        if (typeof docEditorInstance.value.downloadAs === 'function') {
            console.log('✅ downloadAs 方法可用');
        } else {
            console.log('❌ downloadAs 方法不可用');
        }

        // 方法4: 提供手动操作指南
        console.log('=== 手动页码跳转指南 ===');
        console.log('由于连接器不可用，请手动执行以下操作：');
        console.log('1. 按 Ctrl+G 打开"转到页面"对话框');
        console.log(`2. 输入页码: ${page}`);
        console.log('3. 按回车确认跳转');
        console.log('或者：');
        console.log('1. 使用编辑器顶部工具栏的页面导航');
        console.log(`2. 直接跳转到第${page}页`);

        // 方法5: 尝试通过 postMessage 与编辑器通信
        const editorFrame = document.querySelector('#docEditor iframe') as HTMLIFrameElement;
        if (editorFrame && editorFrame.contentWindow) {
            console.log('✅ 找到编辑器 iframe，尝试发送消息...');
            editorFrame.contentWindow.postMessage({
                type: 'jumpToPage',
                page: page,
                timestamp: Date.now()
            }, '*');
            console.log('📤 页码跳转消息已发送');
        } else {
            console.log('❌ 未找到编辑器 iframe');
        }

        message.success(`页码跳转测试完成 - 目标第${page}页（请查看控制台获取详细信息）`);

    } catch (error) {
        console.error('❌ 页码跳转测试失败:', error);
        message.error('页码跳转测试失败');
    }
};

// 测试高亮功能 - 完全模仿 docs/index.html 的实现
const testHighlight = async (data: any) => {
    console.log(`=== 跳转到第${data.page}页并高亮选区 ===`);
    console.log('高亮数据:', data);

    // 检查连接器是否可用
    if (!connector.value) {
        console.error('❌ 连接器不可用');
        message.error('连接器未准备就绪，请等待文档加载完成');
        return;
    }

    try {
        // 先执行页码跳转
        await testJumpPage(data.page);

        // 等待一下让页面跳转完成
        setTimeout(() => {
            console.log('✅ 开始高亮处理...');

            // 使用 callCommand 实现高亮功能（模仿 docs/index.html）
            connector.value.callCommand(function() {
                console.log('📝 开始在文档中插入高亮标记...');

                var oDocument = Api.GetDocument();

                // 创建高亮标记段落
                var oParagraph = Api.CreateParagraph();
                var highlightText = `🔴 [高亮选区测试] 🔴\n`;
                highlightText += `📍 目标页码: ${data.page}\n`;
                highlightText += `📐 选区数量: ${data.position ? data.position.length : 0}\n`;

                if (data.position && data.position.length > 0) {
                    data.position.forEach((pos, index) => {
                        highlightText += `\n区域 ${index + 1}:\n`;
                        highlightText += `  页码: ${pos.page}\n`;
                        highlightText += `  坐标: (${pos.x1}, ${pos.y1}) → (${pos.x2}, ${pos.y2})\n`;
                        highlightText += `  大小: ${pos.x2 - pos.x1} × ${pos.y2 - pos.y1}\n`;
                    });
                }

                highlightText += `\n⏰ 时间: ${new Date().toLocaleString()}\n`;

                oParagraph.AddText(highlightText);

                // 设置红色边框来模拟高亮效果（模仿 docs/index.html）
                oParagraph.SetLeftBorder("single", 24, 0, 255, 111, 61);
                oParagraph.SetRightBorder("single", 24, 0, 255, 111, 61);
                oParagraph.SetTopBorder("single", 24, 0, 255, 111, 61);
                oParagraph.SetBottomBorder("single", 24, 0, 255, 111, 61);

                // 设置段落间距
                oParagraph.SetSpacingAfter(200);
                oParagraph.SetSpacingBefore(200);

                // 将段落添加到文档
                oDocument.Push(oParagraph);

                console.log('✅ 高亮标记插入成功！');
            });
        }, 1000);

        message.success(`高亮测试完成 - 页码: ${data.page}, 区域数: ${data.position ? data.position.length : 0}`);

    } catch (error) {
        console.error('❌ 高亮功能失败:', error);
        message.error('高亮功能失败');
    }
};


// JWT令牌生成
const setupJWT = () => {
    const payload = {
        document: config.document,
        documentType: config.documentType,
        editorConfig: config.editorConfig,
        height: "100%",
        width: "100%"
    };
    
    // 使用与docs/index.html相同的密钥
    const secret = 'jwt@bos6688';
    config.token = createJWT(payload, secret);
};

/**
 * 生成 ONLYOFFICE 需要的 JWT（HS256）
 * @param {object} payload  ONLYOFFICE 配置对象
 * @param {string} secret   与 ONLYOFFICE 服务端一致的密钥
 * @returns {string} 标准 JWT
 */
const createJWT = (payload: any, secret: string): string => {
    if (!secret) return '';

    const header = { alg: 'HS256', typ: 'JWT' };
    const headerEnc = base64url(JSON.stringify(header));
    const payloadEnc = base64url(JSON.stringify(payload));

    const signature = CryptoJS.HmacSHA256(`${headerEnc}.${payloadEnc}`, secret);
    const signatureEnc = signature.toString(CryptoJS.enc.Base64)
        .replace(/\+/g, '-')
        .replace(/\//g, '_')
        .replace(/=/g, '');

    return `${headerEnc}.${payloadEnc}.${signatureEnc}`;
};

/**
 * Base64URL编码
 * @param {string} data 要编码的数据
 * @returns {string} Base64URL编码后的字符串
 */
const base64url = (data: string): string => {
    // 使用CryptoJS进行Base64编码
    const base64 = CryptoJS.enc.Base64.stringify(
        CryptoJS.enc.Utf8.parse(data)
    );
    
    // 转换为Base64URL格式
    return base64
        .replace(/\+/g, '-')
        .replace(/\//g, '_')
        .replace(/=/g, '');
}; 

onMounted(async () => {
    // 生成JWT令牌
    setupJWT();

    // 添加消息监听器，用于接收来自编辑器的消息
    const messageHandler = (event: MessageEvent) => {
        // 只处理来自编辑器的消息
        if (event.origin.includes('************') || event.origin.includes('localhost')) {
            console.log('收到来自编辑器的消息:', event.data);

            // 处理不同类型的消息
            if (event.data && typeof event.data === 'object') {
                switch (event.data.type) {
                    case 'jumpToPageResponse':
                        console.log('页码跳转响应:', event.data);
                        break;
                    case 'highlightResponse':
                        console.log('高亮响应:', event.data);
                        break;
                    default:
                        console.log('其他消息:', event.data);
                }
            }
        }
    };

    window.addEventListener('message', messageHandler);

    // 存储消息处理器引用，用于清理
    (window as any).editorMessageHandler = messageHandler;

    nextTick(() => {
        isComponentReady.value = true;
    });
});

// 组件卸载时清理监听器
onUnmounted(() => {
    // 清理消息监听器
    if ((window as any).editorMessageHandler) {
        window.removeEventListener('message', (window as any).editorMessageHandler);
        delete (window as any).editorMessageHandler;
    }
});
</script>

<style scoped>
.document-editor-wrapper {
    display: flex;
    width: 100%;
    height: 100vh;
}

.document-editor-container {
    flex: 1;
    height: 100vh;
    position: relative;
    border-right: 1px solid #e8e8e8;
}

.loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    font-size: 16px;
    color: #666;
}

#docEditor {
    width: 100%;
    height: 100%;
}

.suggestion-panel {
    width: 350px;
    height: 100vh;
    overflow-y: auto;
    background-color: #f9f9f9;
    padding: 16px;
    box-sizing: border-box;
}

.suggestion-panel h3 {
    margin-top: 0;
    margin-bottom: 16px;
    font-size: 18px;
    color: #333;
    border-bottom: 1px solid #e8e8e8;
    padding-bottom: 8px;
} 

.suggestion-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.suggestion-item {
    background-color: white;
    border-radius: 4px;
    padding: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    cursor: pointer;
    transition: all 0.3s;
    border: 1px solid #e8e8e8;
}

.suggestion-item:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.suggestion-item.active {
    border-color: #1890ff;
    background-color: #e6f7ff;
}

.suggestion-content {
    margin-bottom: 12px;
}

.suggestion-title {
    font-weight: bold;
    margin-bottom: 8px;
    color: #333;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.suggestion-status {
    font-size: 12px;
    padding: 2px 8px;
    border-radius: 12px;
    font-weight: normal;
}

.suggestion-status.pending {
    background-color: #fff7e6;
    color: #fa8c16;
    border: 1px solid #ffd591;
}

.suggestion-status.accepted {
    background-color: #f6ffed;
    color: #52c41a;
    border: 1px solid #b7eb8f;
}

.suggestion-status.rejected {
    background-color: #fff2f0;
    color: #ff4d4f;
    border: 1px solid #ffccc7;
}

.suggestion-text {
    font-size: 14px;
    color: #666;
}

.original-text, .suggested-text {
    margin-bottom: 4px;
    word-break: break-all;
}

.original-text {
    color: #ff4d4f;
    text-decoration: line-through;
}

.suggested-text {
    color: #52c41a;
}

.location {
    font-size: 12px;
    color: #999;
    margin-top: 8px;
    line-height: 1.4;
}

.location > div {
    margin-bottom: 2px;
}

.suggestion-actions {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
}

.accept-btn, .reject-btn {
    padding: 4px 12px;
    border-radius: 4px;
    border: none;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s;
}

.accept-btn {
    background-color: #52c41a;
    color: white;
}

.accept-btn:hover {
    background-color: #73d13d;
}

.reject-btn {
    background-color: #f5f5f5;
    color: #666;
}

.reject-btn:hover {
    background-color: #e8e8e8;
}
</style>

