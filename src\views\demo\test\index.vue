<template>
    <div class="document-editor-wrapper">
        <div class="document-editor-container">
            <DocumentEditor
                v-if="isComponentReady"
                id="docEditor"
                document-server-url="http://172.16.0.198:9066/"
                :config="config"
                :events="config.events"
                :on-load-component-error="onLoadComponentError"
            />
            <div v-else class="loading">
                正在加载文档编辑器...
                <!-- 9066自动化，9070社区版 -->
            </div>
        </div>
        <div class="suggestion-panel">
            <h3>修改建议列表</h3>
            <div class="test-controls">
                <button class="test-btn" @click="testExecuteMethod">executeMethod测试</button>
            </div>
            <div class="suggestion-list">
                <div 
                    v-for="(suggestion, index) in suggestionList" 
                    :key="index" 
                    class="suggestion-item"
                    :class="{ 'active': selectedSuggestion === index }"
                    @click="selectSuggestion(index)"
                >
                    <div class="suggestion-content">
                        <div class="suggestion-title">
                            修改建议 #{{ index + 1 }}
                            <span class="suggestion-status" :class="suggestion.status">
                                {{ suggestion.status === 'pending' ? '待处理' : suggestion.status === 'accepted' ? '已接受' : '已拒绝' }}
                            </span>
                        </div>
                        <div class="suggestion-text">
                            <div class="original-text">原文: {{ suggestion.originalText }}</div>
                            <div class="suggested-text">建议: {{ suggestion.suggestedText }}</div>
                            <div class="location">
                                <div>页码: 第{{ suggestion.pageNumber }}页</div>
                                <div>段落: 第{{ suggestion.paragraphIndex }}段</div>
                                <div>字符: {{ suggestion.startIndex }}-{{ suggestion.endIndex }}</div>
                                <div>坐标: ({{ suggestion.coordinates.x1 }},{{ suggestion.coordinates.y1 }}) - ({{ suggestion.coordinates.x2 }},{{ suggestion.coordinates.y2 }})</div>
                            </div>
                        </div>
                    </div>
                    <div class="suggestion-actions">
                        <button class="accept-btn" @click.stop="acceptSuggestion(index)">接受</button>
                        <button class="reject-btn" @click.stop="rejectSuggestion(index)">拒绝</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, nextTick } from 'vue';
import { DocumentEditor } from '@onlyoffice/document-editor-vue';
import { message } from 'ant-design-vue';
import CryptoJS from 'crypto-js'; 
const isComponentReady = ref(false);
const docEditorInstance = ref<any>(null);
const connector = ref(null);
const selectedSuggestion = ref<number | null>(null);

// 示例修改建议列表 - 包含页码和坐标信息
const suggestionList = reactive([
    {
        id: 1,
        pageNumber: 1, // 页码
        coordinates: { // 对角线坐标 (左上角和右下角)
            x1: 100,
            y1: 200,
            x2: 300,
            y2: 250
        },
        paragraphIndex: 2,
        startIndex: 5,
        endIndex: 15,
        originalText: "这是原始文本",
        suggestedText: "这是建议修改后的文本",
        color: "red",
        status: "pending" // pending, accepted, rejected
    },
    {
        id: 2,
        pageNumber: 2,
        coordinates: {
            x1: 150,
            y1: 300,
            x2: 400,
            y2: 350
        },
        paragraphIndex: 5,
        startIndex: 10,
        endIndex: 25,
        originalText: "需要修改的内容示例",
        suggestedText: "已优化的内容示例",
        color: "red",
        status: "pending"
    },
    {
        id: 3,
        pageNumber: 3,
        coordinates: {
            x1: 80,
            y1: 150,
            x2: 350,
            y2: 200
        },
        paragraphIndex: 8,
        startIndex: 3,
        endIndex: 20,
        originalText: "这里有一些错误的表述",
        suggestedText: "这里是更准确的表述",
        color: "red",
        status: "pending"
    }
]);
// onlyoffice配置
const config = reactive({
    document: {
        fileType: 'docx',
        key: 'demo_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9),
        title: '测试文件.docx',
        // url: 'https://sppgpttest.gcycloud.cn/sftp/file/test/default/20250721/1947172506146705408/测试文件.docx',
        url:'http://172.16.0.116:8090/files/test.docx',
        permissions: {
            edit: true,
            download: true,
            print: true,
            review: true,
            comment: true
        }
    },
    documentType: 'word',
    token: '' as string, // JWT token
    editorConfig: {
        mode: 'edit',
        lang: 'zh-CN',
        customization: {
            autosave: false,
            forcesave: true,
            features: {
                spellcheck: false,
                ruler: true,
                pageNavigation: false,
                leftMenu: false,
                rightMenu: false,
                header: true,
                footer: true
            }
        },
        user: {
            id: 'user_' + Date.now(),
            name: '测试用户'
        },
        // 添加回调URL
        callbackUrl: "http://172.16.0.198:18000/callback"
    },
    events: {
        onAppReady: (event: any) => {
            console.log('OnlyOffice 应用已准备就绪', event);
        },
        onDocumentReady: (event: any) => {
            console.log('文档加载成功', event); 
            // 获取文档编辑器实例 - 使用event.target
            if (event && event.target) {
                docEditorInstance.value = event.target; 
            } else {
                console.error('无法获取文档编辑器实例'); 
            }
        },
        onError: (event: any) => {
            console.error('OnlyOffice错误:', event);
        },
        onInfo: (event: any) => {
            console.log('OnlyOffice信息:', event);
        }
    }
}); 

const onLoadComponentError = (errorCode: number, errorDescription: string) => {
    console.error('OnlyOffice错误:', errorCode, errorDescription);
    
    if (errorCode === -2) {
        message.error('文档服务器连接失败，请检查服务器地址');
    } else if (errorCode === -3) {
        message.error('文档安全令牌错误，请联系管理员');
    } else {
        message.error(`文档编辑器错误: ${errorDescription}`);
    }
};
// JWT令牌生成
const setupJWT = () => {
    const payload = {
        document: config.document,
        documentType: config.documentType,
        editorConfig: config.editorConfig,
        height: "100%",
        width: "100%"
    };
    
    // 使用与docs/index.html相同的密钥
    const secret = 'jwt@bos6688';
    config.token = createJWT(payload, secret);
};

/**
 * 生成 ONLYOFFICE 需要的 JWT（HS256）
 * @param {object} payload  ONLYOFFICE 配置对象
 * @param {string} secret   与 ONLYOFFICE 服务端一致的密钥
 * @returns {string} 标准 JWT
 */
const createJWT = (payload: any, secret: string): string => {
    if (!secret) return '';

    const header = { alg: 'HS256', typ: 'JWT' };
    const headerEnc = base64url(JSON.stringify(header));
    const payloadEnc = base64url(JSON.stringify(payload));

    const signature = CryptoJS.HmacSHA256(`${headerEnc}.${payloadEnc}`, secret);
    const signatureEnc = signature.toString(CryptoJS.enc.Base64)
        .replace(/\+/g, '-')
        .replace(/\//g, '_')
        .replace(/=/g, '');

    return `${headerEnc}.${payloadEnc}.${signatureEnc}`;
};

/**
 * Base64URL编码
 * @param {string} data 要编码的数据
 * @returns {string} Base64URL编码后的字符串
 */
const base64url = (data: string): string => {
    // 使用CryptoJS进行Base64编码
    const base64 = CryptoJS.enc.Base64.stringify(
        CryptoJS.enc.Utf8.parse(data)
    );
    
    // 转换为Base64URL格式
    return base64
        .replace(/\+/g, '-')
        .replace(/\//g, '_')
        .replace(/=/g, '');
}; 

onMounted(async () => {
    // 生成JWT令牌
    setupJWT(); 
    nextTick(() => {
        isComponentReady.value = true;
    });
});

// 选择建议项并跳转到对应页码
const selectSuggestion = async (index: number) => {
    selectedSuggestion.value = index;
    const suggestion = suggestionList[index];

    console.log(`=== 选择建议 #${index + 1} ===`);
    console.log('建议详情:', suggestion);

    // 跳转到指定页码并高亮选区
    await jumpToPageAndHighlight(suggestion);
};

// 跳转到指定页码并高亮选区的核心方法
const jumpToPageAndHighlight = async (suggestion: any) => {
    if (!docEditorInstance.value) {
        console.error('文档编辑器实例不可用');
        message.error('文档编辑器未准备就绪');
        return;
    }

    try {
        console.log(`=== 开始跳转到第${suggestion.pageNumber}页 ===`);

        // 检查是否有executeMethod方法
        if (typeof docEditorInstance.value.executeMethod === 'function') {
            console.log('executeMethod方法可用，开始执行页码跳转...');

            // 1. 插入跳转标记文本
            const suggestionIndex = selectedSuggestion.value !== null ? selectedSuggestion.value + 1 : 0;
            const jumpMarkerText = `[跳转到第${suggestion.pageNumber}页 - 建议#${suggestionIndex}]\n` +
                `原文: ${suggestion.originalText}\n` +
                `建议: ${suggestion.suggestedText}\n` +
                `坐标: (${suggestion.coordinates.x1},${suggestion.coordinates.y1})-(${suggestion.coordinates.x2},${suggestion.coordinates.y2})\n` +
                `时间: ${new Date().toLocaleString()}\n\n`;

            // 插入标记文本
            const insertResult = await executeMethodSafely('PasteText', [jumpMarkerText]);
            console.log('页码跳转标记插入结果:', insertResult);

            // 2. 尝试全选文档（模拟高亮效果）
            const selectAllResult = await executeMethodSafely('SelectAll', []);
            console.log('全选结果:', selectAllResult);

            // 3. 移动光标到文档末尾
            const moveCursorResult = await executeMethodSafely('MoveCursorToEnd', []);
            console.log('移动光标到末尾结果:', moveCursorResult);

            // 4. 尝试插入分页符（如果支持的话）
            const insertPageBreakResult = await executeMethodSafely('InsertPageBreak', []);
            console.log('插入分页符结果:', insertPageBreakResult);

            message.success(`已跳转到第${suggestion.pageNumber}页并标记选区`);

        } else {
            console.error('executeMethod方法不可用');
            message.error('文档编辑器API不支持页码跳转功能');
        }

    } catch (error) {
        console.error('页码跳转过程中发生错误:', error);
        message.error('页码跳转失败，请查看控制台了解详情');
    }
};

// 安全执行executeMethod的辅助方法
const executeMethodSafely = async (methodName: string, parameters: any[] = []) => {
    return new Promise((resolve) => {
        try {
            docEditorInstance.value.executeMethod(methodName, parameters, (result: any) => {
                resolve(result);
            });
        } catch (error) {
            console.error(`执行${methodName}方法时出错:`, error);
            resolve(false);
        }
    });
};

// 接受建议
const acceptSuggestion = (index: number) => {
    suggestionList[index].status = 'accepted';
    console.log(`接受建议 #${index + 1}`);
    message.success(`已接受建议 #${index + 1}`);
};

// 拒绝建议
const rejectSuggestion = (index: number) => {
    suggestionList[index].status = 'rejected';
    console.log(`拒绝建议 #${index + 1}`);
    message.success(`已拒绝建议 #${index + 1}`);
};

// executeMethod测试方法
const testExecuteMethod = async () => {
    if (!docEditorInstance.value) {
        console.error('文档编辑器实例不可用');
        return;
    }

    console.log('=== executeMethod页码跳转测试开始 ===');

    if (typeof docEditorInstance.value.executeMethod === 'function') {
        console.log('executeMethod方法可用，开始测试...');

        const testText = `[executeMethod测试 - ${new Date().toLocaleString()}]\n` +
            `这是一个页码跳转功能测试标记\n` +
            `测试内容：文本插入、全选、光标移动、分页符插入\n\n`;

        // 测试各种executeMethod功能
        const insertResult = await executeMethodSafely('PasteText', [testText]);
        console.log('页码跳转标记插入结果:', insertResult);

        const selectAllResult = await executeMethodSafely('SelectAll', []);
        console.log('全选结果:', selectAllResult);

        const moveCursorResult = await executeMethodSafely('MoveCursorToEnd', []);
        console.log('移动光标到末尾结果:', moveCursorResult);

        const insertPageBreakResult = await executeMethodSafely('InsertPageBreak', []);
        console.log('插入分页符结果:', insertPageBreakResult);

    } else {
        console.error('executeMethod方法不可用');
    }
};

// 组件卸载时清理监听器
onUnmounted(() => {
});
</script>

<style scoped>
.document-editor-wrapper {
    display: flex;
    width: 100%;
    height: 100vh;
}

.document-editor-container {
    flex: 1;
    height: 100vh;
    position: relative;
    border-right: 1px solid #e8e8e8;
}

.loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    font-size: 16px;
    color: #666;
}

#docEditor {
    width: 100%;
    height: 100%;
}

.suggestion-panel {
    width: 350px;
    height: 100vh;
    overflow-y: auto;
    background-color: #f9f9f9;
    padding: 16px;
    box-sizing: border-box;
}

.suggestion-panel h3 {
    margin-top: 0;
    margin-bottom: 16px;
    font-size: 18px;
    color: #333;
    border-bottom: 1px solid #e8e8e8;
    padding-bottom: 8px;
}

.test-controls {
    margin-bottom: 16px;
}

.test-btn {
    padding: 8px 16px;
    background-color: #1890ff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s;
}

.test-btn:hover {
    background-color: #40a9ff;
}

.suggestion-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.suggestion-item {
    background-color: white;
    border-radius: 4px;
    padding: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    cursor: pointer;
    transition: all 0.3s;
    border: 1px solid #e8e8e8;
}

.suggestion-item:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.suggestion-item.active {
    border-color: #1890ff;
    background-color: #e6f7ff;
}

.suggestion-content {
    margin-bottom: 12px;
}

.suggestion-title {
    font-weight: bold;
    margin-bottom: 8px;
    color: #333;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.suggestion-status {
    font-size: 12px;
    padding: 2px 8px;
    border-radius: 12px;
    font-weight: normal;
}

.suggestion-status.pending {
    background-color: #fff7e6;
    color: #fa8c16;
    border: 1px solid #ffd591;
}

.suggestion-status.accepted {
    background-color: #f6ffed;
    color: #52c41a;
    border: 1px solid #b7eb8f;
}

.suggestion-status.rejected {
    background-color: #fff2f0;
    color: #ff4d4f;
    border: 1px solid #ffccc7;
}

.suggestion-text {
    font-size: 14px;
    color: #666;
}

.original-text, .suggested-text {
    margin-bottom: 4px;
    word-break: break-all;
}

.original-text {
    color: #ff4d4f;
    text-decoration: line-through;
}

.suggested-text {
    color: #52c41a;
}

.location {
    font-size: 12px;
    color: #999;
    margin-top: 8px;
    line-height: 1.4;
}

.location > div {
    margin-bottom: 2px;
}

.suggestion-actions {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
}

.accept-btn, .reject-btn {
    padding: 4px 12px;
    border-radius: 4px;
    border: none;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s;
}

.accept-btn {
    background-color: #52c41a;
    color: white;
}

.accept-btn:hover {
    background-color: #73d13d;
}

.reject-btn {
    background-color: #f5f5f5;
    color: #666;
}

.reject-btn:hover {
    background-color: #e8e8e8;
}
</style>

