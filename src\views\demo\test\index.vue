<template>
    <div class="document-editor-wrapper">
        <div class="document-editor-container">
            <DocumentEditor
                v-if="isComponentReady"
                id="docEditor"
                document-server-url="http://172.16.0.198:9066/"
                :config="config"
                :events="config.events"
                :on-load-component-error="onLoadComponentError"
            />
            <div v-else class="loading">
                正在加载文档编辑器...
                <!-- 9066自动化，9070社区版 -->
            </div>
        </div>
        <div class="suggestion-panel"> 
            <div class="suggestion-list">
                <a-button type="primary" @click="testJumpPage(10)">跳转页码</a-button>
                <a-button type="primary" @click="testHighlight(
                    {
                    page:2,position:[{
                    page: 4,
                    x1: 77, y1: 249, x2: 536, y2: 279
                }]})">跳转2页，第4页有高亮选区</a-button>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, nextTick } from 'vue';
import { DocumentEditor } from '@onlyoffice/document-editor-vue';
import { message } from 'ant-design-vue';
import CryptoJS from 'crypto-js'; 
const isComponentReady = ref(false);
const docEditorInstance = ref<any>(null);
const connector = ref<any>(null);
// onlyoffice配置
const config = reactive({
    document: {
        fileType: 'docx',
        key: 'demo_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9),
        title: '测试文件.docx',
        // url: 'https://sppgpttest.gcycloud.cn/sftp/file/test/default/20250721/1947172506146705408/测试文件.docx',
        url:'http://************:8090/files/test.docx',
        permissions: {
            edit: true,
            download: true,
            print: true,
            review: true,
            comment: true
        }
    },
    documentType: 'word',
    token: '' as string, // JWT token
    editorConfig: {
        mode: 'edit',
        lang: 'zh-CN',
        customization: {
            autosave: false,
            forcesave: true,
            features: {
                spellcheck: false,
                ruler: true,
                pageNavigation: false,
                leftMenu: false,
                rightMenu: false,
                header: true,
                footer: true
            }
        },
        user: {
            id: 'user_' + Date.now(),
            name: '测试用户'
        },
        // 添加回调URL
        callbackUrl: "http://172.16.0.198:18000/callback"
    },
    events: {
        onAppReady: (event: any) => {
            // console.log('OnlyOffice 应用已准备就绪', event);
        },
        onDocumentReady: (event: any) => {
            console.log('文档加载成功', event);
            // 获取文档编辑器实例 - 使用event.target
            if (event && event.target) {
                docEditorInstance.value = event.target;
                console.log('文档编辑器实例已获取:', docEditorInstance.value);

                // 尝试创建连接器（如果支持的话）
                try {
                    if (typeof docEditorInstance.value.createConnector === 'function') {
                        connector.value = docEditorInstance.value.createConnector();
                        console.log('连接器已创建:', connector.value);
                    }
                } catch (error) {
                    console.log('连接器创建失败或不支持:', error);
                }

            } else {
                console.error('无法获取文档编辑器实例');
            }
        },
        onError: (event: any) => {
            console.error('OnlyOffice错误:', event);
        },
        onInfo: (event: any) => {
            // console.log('OnlyOffice信息:', event);
        }
    }
}); 

const onLoadComponentError = (errorCode: number, errorDescription: string) => {
    console.error('OnlyOffice错误:', errorCode, errorDescription);
    
    if (errorCode === -2) {
        message.error('文档服务器连接失败，请检查服务器地址');
    } else if (errorCode === -3) {
        message.error('文档安全令牌错误，请联系管理员');
    } else {
        message.error(`文档编辑器错误: ${errorDescription}`);
    }
};
// 测试页码跳转功能
const testJumpPage = async (page: number) => {
    if (!docEditorInstance.value) {
        console.error('文档编辑器实例不可用');
        message.error('文档编辑器未准备就绪');
        return;
    }

    console.log(`=== 跳转到第${page}页 ===`);

    try {
        // 使用 OnlyOffice 的 executeMethod API 跳转页码
        if (typeof docEditorInstance.value.executeMethod === 'function') {

            // 插入页码跳转标记和内容
            const jumpMarkerText = `\n📄 [跳转到第${page}页测试] 📄\n` +
                `⏰ 时间: ${new Date().toLocaleString()}\n` +
                `🎯 目标页码: ${page}\n` +
                `${'='.repeat(40)}\n\n`;

            // 插入标记文本
            const insertResult = await executeMethodSafely('PasteText', [jumpMarkerText]);
            console.log('插入标记文本结果:', insertResult);

            // 尝试多种页码跳转方法
            const methods = [
                { name: 'GoToPage', params: [page] },
                { name: 'MoveCursorToPage', params: [page] },
                { name: 'SetCurrentPage', params: [page] },
                { name: 'GotoPage', params: [page] },
                { name: 'MoveToPage', params: [page] }
            ];

            for (const method of methods) {
                const result = await executeMethodSafely(method.name, method.params);
                if (result) {
                    console.log(`${method.name} 方法执行成功`);
                    break;
                }
            }

            message.success(`页码跳转测试完成，目标第${page}页`);
        } else {
            console.error('executeMethod方法不可用');
            message.error('文档编辑器API不支持executeMethod功能');
        }
    } catch (error) {
        console.error('页码跳转失败:', error);
        message.error('页码跳转失败');
    }
};

// 测试高亮功能
const testHighlight = async (data: any) => {
    if (!docEditorInstance.value) {
        console.error('文档编辑器实例不可用');
        message.error('文档编辑器未准备就绪');
        return;
    }

    console.log(`=== 跳转到第${data.page}页并高亮选区 ===`);
    console.log('高亮数据:', data);

    try {
        // 先跳转到指定页码
        await testJumpPage(data.page);

        // 等待一下让页面跳转完成
        await new Promise(resolve => setTimeout(resolve, 1500));

        // 插入高亮测试标记
        const highlightTestText = `\n🔴 [高亮选区测试开始] 🔴\n` +
            `📍 目标页码: ${data.page}\n` +
            `📐 选区数量: ${data.position ? data.position.length : 0}\n` +
            `⏰ 时间: ${new Date().toLocaleString()}\n`;

        await executeMethodSafely('PasteText', [highlightTestText]);

        // 处理高亮选区
        if (data.position && data.position.length > 0) {
            for (let i = 0; i < data.position.length; i++) {
                const pos = data.position[i];
                console.log(`处理第${i + 1}个高亮区域:`, pos);
                await highlightRegion(pos, i + 1);

                // 每个区域之间稍作延迟
                await new Promise(resolve => setTimeout(resolve, 500));
            }
        }

        // 插入完成标记
        const completeText = `\n✅ [高亮选区测试完成] ✅\n` +
            `${'='.repeat(50)}\n\n`;
        await executeMethodSafely('PasteText', [completeText]);

        message.success(`已跳转到第${data.page}页并完成高亮选区测试`);
    } catch (error) {
        console.error('高亮功能失败:', error);
        message.error('高亮功能失败');
    }
};

// 高亮指定区域
const highlightRegion = async (position: any, index: number = 1) => {
    console.log(`=== 高亮第${position.page}页的区域 #${index} ===`);
    console.log('坐标:', position);

    try {
        if (typeof docEditorInstance.value.executeMethod === 'function') {
            // 插入高亮区域标记
            const highlightMarkerText = `\n🔴 [第${position.page}页高亮区域 #${index}] 🔴\n` +
                `📐 坐标: (${position.x1}, ${position.y1}) → (${position.x2}, ${position.y2})\n` +
                `📏 区域大小: ${position.x2 - position.x1} × ${position.y2 - position.y1}\n` +
                `⏰ 时间: ${new Date().toLocaleString()}\n`;

            await executeMethodSafely('PasteText', [highlightMarkerText]);

            // 尝试多种高亮方法
            const highlightMethods = [
                { name: 'SelectByCoordinates', params: [position.x1, position.y1, position.x2, position.y2] },
                { name: 'SelectRegion', params: [position.x1, position.y1, position.x2, position.y2] },
                { name: 'SetSelection', params: [position.x1, position.y1, position.x2, position.y2] }
            ];

            for (const method of highlightMethods) {
                const result = await executeMethodSafely(method.name, method.params);
                if (result) {
                    console.log(`${method.name} 选择方法执行成功`);
                    break;
                }
            }

            // 尝试设置高亮颜色
            const colorMethods = [
                { name: 'SetHighlightColor', params: ['#ff0000'] },
                { name: 'SetBackgroundColor', params: ['#ff0000'] },
                { name: 'SetTextColor', params: ['#ff0000'] }
            ];

            for (const method of colorMethods) {
                await executeMethodSafely(method.name, method.params);
            }

            // 尝试应用高亮
            await executeMethodSafely('ApplyHighlight', []);

            // 插入分隔线
            await executeMethodSafely('PasteText', [`${'─'.repeat(30)}\n\n`]);

        }
    } catch (error) {
        console.error('区域高亮失败:', error);
    }
};

// 安全执行 executeMethod 的辅助方法
const executeMethodSafely = async (methodName: string, parameters: any[] = []) => {
    return new Promise((resolve) => {
        try {
            if (docEditorInstance.value && typeof docEditorInstance.value.executeMethod === 'function') {
                docEditorInstance.value.executeMethod(methodName, parameters, (result: any) => {
                    console.log(`${methodName} 执行结果:`, result);
                    resolve(result);
                });
            } else {
                console.warn(`executeMethod 不可用或 ${methodName} 方法不存在`);
                resolve(false);
            }
        } catch (error) {
            console.error(`执行 ${methodName} 方法时出错:`, error);
            resolve(false);
        }
    });
};
// JWT令牌生成
const setupJWT = () => {
    const payload = {
        document: config.document,
        documentType: config.documentType,
        editorConfig: config.editorConfig,
        height: "100%",
        width: "100%"
    };
    
    // 使用与docs/index.html相同的密钥
    const secret = 'jwt@bos6688';
    config.token = createJWT(payload, secret);
};

/**
 * 生成 ONLYOFFICE 需要的 JWT（HS256）
 * @param {object} payload  ONLYOFFICE 配置对象
 * @param {string} secret   与 ONLYOFFICE 服务端一致的密钥
 * @returns {string} 标准 JWT
 */
const createJWT = (payload: any, secret: string): string => {
    if (!secret) return '';

    const header = { alg: 'HS256', typ: 'JWT' };
    const headerEnc = base64url(JSON.stringify(header));
    const payloadEnc = base64url(JSON.stringify(payload));

    const signature = CryptoJS.HmacSHA256(`${headerEnc}.${payloadEnc}`, secret);
    const signatureEnc = signature.toString(CryptoJS.enc.Base64)
        .replace(/\+/g, '-')
        .replace(/\//g, '_')
        .replace(/=/g, '');

    return `${headerEnc}.${payloadEnc}.${signatureEnc}`;
};

/**
 * Base64URL编码
 * @param {string} data 要编码的数据
 * @returns {string} Base64URL编码后的字符串
 */
const base64url = (data: string): string => {
    // 使用CryptoJS进行Base64编码
    const base64 = CryptoJS.enc.Base64.stringify(
        CryptoJS.enc.Utf8.parse(data)
    );
    
    // 转换为Base64URL格式
    return base64
        .replace(/\+/g, '-')
        .replace(/\//g, '_')
        .replace(/=/g, '');
}; 

onMounted(async () => {
    // 生成JWT令牌
    setupJWT(); 
    nextTick(() => {
        isComponentReady.value = true;
    });
});

// 组件卸载时清理监听器
onUnmounted(() => { 
});
</script>

<style scoped>
.document-editor-wrapper {
    display: flex;
    width: 100%;
    height: 100vh;
}

.document-editor-container {
    flex: 1;
    height: 100vh;
    position: relative;
    border-right: 1px solid #e8e8e8;
}

.loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    font-size: 16px;
    color: #666;
}

#docEditor {
    width: 100%;
    height: 100%;
}

.suggestion-panel {
    width: 350px;
    height: 100vh;
    overflow-y: auto;
    background-color: #f9f9f9;
    padding: 16px;
    box-sizing: border-box;
}

.suggestion-panel h3 {
    margin-top: 0;
    margin-bottom: 16px;
    font-size: 18px;
    color: #333;
    border-bottom: 1px solid #e8e8e8;
    padding-bottom: 8px;
} 

.suggestion-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.suggestion-item {
    background-color: white;
    border-radius: 4px;
    padding: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    cursor: pointer;
    transition: all 0.3s;
    border: 1px solid #e8e8e8;
}

.suggestion-item:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.suggestion-item.active {
    border-color: #1890ff;
    background-color: #e6f7ff;
}

.suggestion-content {
    margin-bottom: 12px;
}

.suggestion-title {
    font-weight: bold;
    margin-bottom: 8px;
    color: #333;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.suggestion-status {
    font-size: 12px;
    padding: 2px 8px;
    border-radius: 12px;
    font-weight: normal;
}

.suggestion-status.pending {
    background-color: #fff7e6;
    color: #fa8c16;
    border: 1px solid #ffd591;
}

.suggestion-status.accepted {
    background-color: #f6ffed;
    color: #52c41a;
    border: 1px solid #b7eb8f;
}

.suggestion-status.rejected {
    background-color: #fff2f0;
    color: #ff4d4f;
    border: 1px solid #ffccc7;
}

.suggestion-text {
    font-size: 14px;
    color: #666;
}

.original-text, .suggested-text {
    margin-bottom: 4px;
    word-break: break-all;
}

.original-text {
    color: #ff4d4f;
    text-decoration: line-through;
}

.suggested-text {
    color: #52c41a;
}

.location {
    font-size: 12px;
    color: #999;
    margin-top: 8px;
    line-height: 1.4;
}

.location > div {
    margin-bottom: 2px;
}

.suggestion-actions {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
}

.accept-btn, .reject-btn {
    padding: 4px 12px;
    border-radius: 4px;
    border: none;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s;
}

.accept-btn {
    background-color: #52c41a;
    color: white;
}

.accept-btn:hover {
    background-color: #73d13d;
}

.reject-btn {
    background-color: #f5f5f5;
    color: #666;
}

.reject-btn:hover {
    background-color: #e8e8e8;
}
</style>

