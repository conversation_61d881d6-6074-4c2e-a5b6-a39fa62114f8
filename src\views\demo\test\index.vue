<template>
    <div class="document-editor-wrapper">
        <div class="document-editor-container">
            <DocumentEditor
                v-if="isComponentReady"
                id="docEditor"
                document-server-url="http://************:9066/"
                :config="config"
                :events="config.events"
                :on-load-component-error="onLoadComponentError"
            />
            <div v-else class="loading">
                正在加载文档编辑器...
                <!-- 9066自动化，9070社区版 -->
            </div>
        </div>
        <div class="suggestion-panel"> 
            <div class="suggestion-list">
                <a-button type="primary" @click="testJumpPage(10)">跳转页码</a-button>
                <a-button type="primary" @click="testHighlight(
                    {
                    page:2,position:[{
                    page: 3,
                    x1: 77, y1: 249, x2: 536, y2: 279
                }]})">跳转2页，第3页有高亮选区</a-button>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, nextTick } from 'vue';
import { DocumentEditor } from '@onlyoffice/document-editor-vue';
import { message } from 'ant-design-vue';
import CryptoJS from 'crypto-js'; 
const isComponentReady = ref(false);
const docEditorInstance = ref<any>(null);
const connector = ref<any>(null);
// onlyoffice配置
const config = reactive({
    document: {
        fileType: 'docx',
        key: 'demo_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9),
        title: '测试文件.docx',
        // url: 'https://sppgpttest.gcycloud.cn/sftp/file/test/default/20250721/1947172506146705408/测试文件.docx',
        url:'http://************:8090/files/test.docx',
        permissions: {
            edit: true,
            download: true,
            print: true,
            review: true,
            comment: true
        }
    },
    documentType: 'word',
    token: '' as string, // JWT token
    editorConfig: {
        mode: 'edit',
        lang: 'zh-CN',
        customization: {
            autosave: false,
            forcesave: true,
            features: {
                spellcheck: false,
                ruler: true,
                pageNavigation: false,
                leftMenu: false,
                rightMenu: false,
                header: true,
                footer: true
            }
        },
        user: {
            id: 'user_' + Date.now(),
            name: '测试用户'
        },
        // 添加回调URL
        callbackUrl: "http://************:18000/callback"
    },
    events: {
        onAppReady: (event: any) => {
            console.log('OnlyOffice 应用已准备就绪', event); 
        },
        onDocumentReady: (event: any) => {
            console.log('文档加载成功', event);

            // 保存编辑器实例到全局变量，方便调试和使用
            if (event && event.target) {
                docEditorInstance.value = event.target;
                (window as any).globalDocEditor = event.target;
                console.log('编辑器实例已保存到全局:', docEditorInstance.value);

                // 尝试创建连接器
                setTimeout(() => {
                    if (typeof docEditorInstance.value.createConnector === 'function') {
                        try {
                            connector.value = docEditorInstance.value.createConnector();
                            (window as any).globalConnector = connector.value;
                            console.log('连接器创建成功:', !!connector.value);
                        } catch (error) {
                            console.error('连接器创建失败:', error);
                        }
                    } else {
                        console.warn('createConnector 不可用 - 可能需要企业版或开发者版');
                    }
                }, 1000);
            }
        },
        onError: (event: any) => {
            console.error('OnlyOffice错误:', event);
        },
        onInfo: (event: any) => {
            // console.log('OnlyOffice信息:', event);
        }
    }
}); 

const onLoadComponentError = (errorCode: number, errorDescription: string) => {
    console.error('OnlyOffice错误:', errorCode, errorDescription);
    
    if (errorCode === -2) {
        message.error('文档服务器连接失败，请检查服务器地址');
    } else if (errorCode === -3) {
        message.error('文档安全令牌错误，请联系管理员');
    } else {
        message.error(`文档编辑器错误: ${errorDescription}`);
    }
};
// 测试页码跳转功能 - 使用多种方法尝试
const testJumpPage = async (page: number) => { 
};

// 测试高亮功能 - 不依赖连接器的实现
const testHighlight = async (data: any) => {
    console.log(`=== 跳转到第${data.page}页并高亮选区 ===`);
    console.log('高亮数据:', data);

    try {
        // 先执行页码跳转
        await testJumpPage(data.page);

        // 等待一下让页面跳转完成
        setTimeout(() => {
            console.log('开始处理高亮选区...');

            // 方法1: 如果连接器可用，使用连接器
            if (connector.value && typeof connector.value.callCommand === 'function') {
                console.log('使用连接器实现高亮...');
                connector.value.callCommand(function() {
                    var oDocument = Api.GetDocument();
                    var highlightText = `🔴 高亮测试 - 页码:${data.page} - ${new Date().toLocaleString()}`;
                    var oParagraph = Api.CreateParagraph();
                    oParagraph.AddText(highlightText);
                    oParagraph.SetLeftBorder("single", 24, 0, 255, 111, 61);
                    oDocument.Push(oParagraph);
                });
            } else {
                console.log('连接器不可用，使用替代方案...');
            }

            // 方法2: 使用 postMessage 与编辑器通信
            const editorFrame = document.querySelector('#docEditor iframe') as HTMLIFrameElement;
            if (editorFrame && editorFrame.contentWindow) {
                console.log('尝试使用 postMessage 发送高亮请求...');
                editorFrame.contentWindow.postMessage({
                    type: 'highlightRegions',
                    page: data.page,
                    positions: data.position,
                    timestamp: Date.now()
                }, '*');
            }

            // 方法3: 详细记录高亮信息
            console.log('=== 高亮区域详情 ===');
            console.log(`目标页码: ${data.page}`);
            console.log(`区域数量: ${data.position ? data.position.length : 0}`);

            if (data.position && data.position.length > 0) {
                data.position.forEach((pos: any, index: number) => {
                    console.log(`区域 ${index + 1}:`);
                    console.log(`  页码: ${pos.page}`);
                    console.log(`  坐标: (${pos.x1}, ${pos.y1}) → (${pos.x2}, ${pos.y2})`);
                    console.log(`  宽度: ${pos.x2 - pos.x1}px`);
                    console.log(`  高度: ${pos.y2 - pos.y1}px`);
                    console.log(`  面积: ${(pos.x2 - pos.x1) * (pos.y2 - pos.y1)}px²`);

                    // 模拟高亮效果的计算
                    const regionInfo = {
                        id: `region_${index + 1}`,
                        page: pos.page,
                        coordinates: {
                            topLeft: { x: pos.x1, y: pos.y1 },
                            bottomRight: { x: pos.x2, y: pos.y2 }
                        },
                        dimensions: {
                            width: pos.x2 - pos.x1,
                            height: pos.y2 - pos.y1
                        },
                        area: (pos.x2 - pos.x1) * (pos.y2 - pos.y1),
                        timestamp: new Date().toISOString()
                    };

                    console.log(`  区域信息:`, regionInfo);
                });
            }

            // 方法4: 尝试使用 docEditor 的其他方法
            if (docEditorInstance.value && typeof docEditorInstance.value.showMessage === 'function') {
                const message = `高亮请求: 页码${data.page}, ${data.position?.length || 0}个区域`;
                docEditorInstance.value.showMessage(message);
            }

        }, 1000);

        message.success(`高亮测试完成 - 页码: ${data.page}, 区域数: ${data.position ? data.position.length : 0}`);

    } catch (error) {
        console.error('高亮功能失败:', error);
        message.error('高亮功能失败');
    }
};


// JWT令牌生成
const setupJWT = () => {
    const payload = {
        document: config.document,
        documentType: config.documentType,
        editorConfig: config.editorConfig,
        height: "100%",
        width: "100%"
    };
    
    // 使用与docs/index.html相同的密钥
    const secret = 'jwt@bos6688';
    config.token = createJWT(payload, secret);
};

/**
 * 生成 ONLYOFFICE 需要的 JWT（HS256）
 * @param {object} payload  ONLYOFFICE 配置对象
 * @param {string} secret   与 ONLYOFFICE 服务端一致的密钥
 * @returns {string} 标准 JWT
 */
const createJWT = (payload: any, secret: string): string => {
    if (!secret) return '';

    const header = { alg: 'HS256', typ: 'JWT' };
    const headerEnc = base64url(JSON.stringify(header));
    const payloadEnc = base64url(JSON.stringify(payload));

    const signature = CryptoJS.HmacSHA256(`${headerEnc}.${payloadEnc}`, secret);
    const signatureEnc = signature.toString(CryptoJS.enc.Base64)
        .replace(/\+/g, '-')
        .replace(/\//g, '_')
        .replace(/=/g, '');

    return `${headerEnc}.${payloadEnc}.${signatureEnc}`;
};

/**
 * Base64URL编码
 * @param {string} data 要编码的数据
 * @returns {string} Base64URL编码后的字符串
 */
const base64url = (data: string): string => {
    // 使用CryptoJS进行Base64编码
    const base64 = CryptoJS.enc.Base64.stringify(
        CryptoJS.enc.Utf8.parse(data)
    );
    
    // 转换为Base64URL格式
    return base64
        .replace(/\+/g, '-')
        .replace(/\//g, '_')
        .replace(/=/g, '');
}; 

onMounted(async () => {
    // 生成JWT令牌
    setupJWT();

    // 添加消息监听器，用于接收来自编辑器的消息
    const messageHandler = (event: MessageEvent) => {
        // 只处理来自编辑器的消息
        if (event.origin.includes('************') || event.origin.includes('localhost')) {
            console.log('收到来自编辑器的消息:', event.data);

            // 处理不同类型的消息
            if (event.data && typeof event.data === 'object') {
                switch (event.data.type) {
                    case 'jumpToPageResponse':
                        console.log('页码跳转响应:', event.data);
                        break;
                    case 'highlightResponse':
                        console.log('高亮响应:', event.data);
                        break;
                    default:
                        console.log('其他消息:', event.data);
                }
            }
        }
    };

    window.addEventListener('message', messageHandler);

    // 存储消息处理器引用，用于清理
    (window as any).editorMessageHandler = messageHandler;

    nextTick(() => {
        isComponentReady.value = true;
    });
});

// 组件卸载时清理监听器
onUnmounted(() => {
    // 清理消息监听器
    if ((window as any).editorMessageHandler) {
        window.removeEventListener('message', (window as any).editorMessageHandler);
        delete (window as any).editorMessageHandler;
    }
});
</script>

<style scoped>
.document-editor-wrapper {
    display: flex;
    width: 100%;
    height: 100vh;
}

.document-editor-container {
    flex: 1;
    height: 100vh;
    position: relative;
    border-right: 1px solid #e8e8e8;
}

.loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    font-size: 16px;
    color: #666;
}

#docEditor {
    width: 100%;
    height: 100%;
}

.suggestion-panel {
    width: 350px;
    height: 100vh;
    overflow-y: auto;
    background-color: #f9f9f9;
    padding: 16px;
    box-sizing: border-box;
}

.suggestion-panel h3 {
    margin-top: 0;
    margin-bottom: 16px;
    font-size: 18px;
    color: #333;
    border-bottom: 1px solid #e8e8e8;
    padding-bottom: 8px;
} 

.suggestion-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.suggestion-item {
    background-color: white;
    border-radius: 4px;
    padding: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    cursor: pointer;
    transition: all 0.3s;
    border: 1px solid #e8e8e8;
}

.suggestion-item:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.suggestion-item.active {
    border-color: #1890ff;
    background-color: #e6f7ff;
}

.suggestion-content {
    margin-bottom: 12px;
}

.suggestion-title {
    font-weight: bold;
    margin-bottom: 8px;
    color: #333;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.suggestion-status {
    font-size: 12px;
    padding: 2px 8px;
    border-radius: 12px;
    font-weight: normal;
}

.suggestion-status.pending {
    background-color: #fff7e6;
    color: #fa8c16;
    border: 1px solid #ffd591;
}

.suggestion-status.accepted {
    background-color: #f6ffed;
    color: #52c41a;
    border: 1px solid #b7eb8f;
}

.suggestion-status.rejected {
    background-color: #fff2f0;
    color: #ff4d4f;
    border: 1px solid #ffccc7;
}

.suggestion-text {
    font-size: 14px;
    color: #666;
}

.original-text, .suggested-text {
    margin-bottom: 4px;
    word-break: break-all;
}

.original-text {
    color: #ff4d4f;
    text-decoration: line-through;
}

.suggested-text {
    color: #52c41a;
}

.location {
    font-size: 12px;
    color: #999;
    margin-top: 8px;
    line-height: 1.4;
}

.location > div {
    margin-bottom: 2px;
}

.suggestion-actions {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
}

.accept-btn, .reject-btn {
    padding: 4px 12px;
    border-radius: 4px;
    border: none;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s;
}

.accept-btn {
    background-color: #52c41a;
    color: white;
}

.accept-btn:hover {
    background-color: #73d13d;
}

.reject-btn {
    background-color: #f5f5f5;
    color: #666;
}

.reject-btn:hover {
    background-color: #e8e8e8;
}
</style>

