<template>
    <div class="document-editor-wrapper">
        <div class="document-editor-container">
            <DocumentEditor
                v-if="isComponentReady"
                id="docEditor"
                document-server-url="http://172.16.0.198:9066/"
                :config="config"
                :events="config.events"
                :on-load-component-error="onLoadComponentError"
            />
            <div v-else class="loading">
                正在加载文档编辑器...
                <!-- 9066自动化，9070社区版 -->
            </div>
        </div>
        <div class="suggestion-panel"> 
            <div class="suggestion-list">
                <a-button type="primary" @click="testJumpPage(10)">跳转页码</a-button>
                <a-button type="primary" @click="testHighlight(
                    {
                    page:2,position:[{
                    page: 4,
                    x1: 77, y1: 249, x2: 536, y2: 279
                }]})">跳转2页，第4页有高亮选区</a-button>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, nextTick } from 'vue';
import { DocumentEditor } from '@onlyoffice/document-editor-vue';
import { message } from 'ant-design-vue';
import CryptoJS from 'crypto-js'; 
const isComponentReady = ref(false);
const docEditorInstance = ref<any>(null);
const connector = ref<any>(null);
// onlyoffice配置
const config = reactive({
    document: {
        fileType: 'docx',
        key: 'demo_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9),
        title: '测试文件.docx',
        // url: 'https://sppgpttest.gcycloud.cn/sftp/file/test/default/20250721/1947172506146705408/测试文件.docx',
        url:'http://************:8090/files/test.docx',
        permissions: {
            edit: true,
            download: true,
            print: true,
            review: true,
            comment: true
        }
    },
    documentType: 'word',
    token: '' as string, // JWT token
    editorConfig: {
        mode: 'edit',
        lang: 'zh-CN',
        customization: {
            autosave: false,
            forcesave: true,
            features: {
                spellcheck: false,
                ruler: true,
                pageNavigation: false,
                leftMenu: false,
                rightMenu: false,
                header: true,
                footer: true
            }
        },
        user: {
            id: 'user_' + Date.now(),
            name: '测试用户'
        },
        // 添加回调URL
        callbackUrl: "http://172.16.0.198:18000/callback"
    },
    events: {
        onAppReady: (event: any) => {
            // console.log('OnlyOffice 应用已准备就绪', event);
        },
        onDocumentReady: (event: any) => {
            console.log('文档加载成功', event);
            // 获取文档编辑器实例
            if (event && event.target) {
                docEditorInstance.value = event.target;
                console.log('文档编辑器实例已获取:', docEditorInstance.value);
                console.log('可用方法:', Object.getOwnPropertyNames(docEditorInstance.value));

                // 尝试创建连接器
                try {
                    if (typeof docEditorInstance.value.createConnector === 'function') {
                        connector.value = docEditorInstance.value.createConnector();
                        console.log('连接器已创建:', connector.value);
                        console.log('连接器方法:', Object.getOwnPropertyNames(connector.value));
                    } else {
                        console.log('createConnector 方法不存在');
                    }
                } catch (error) {
                    console.log('连接器创建失败:', error);
                }

            } else {
                console.error('无法获取文档编辑器实例');
            }
        },
        onError: (event: any) => {
            console.error('OnlyOffice错误:', event);
        },
        onInfo: (event: any) => {
            // console.log('OnlyOffice信息:', event);
        }
    }
}); 

const onLoadComponentError = (errorCode: number, errorDescription: string) => {
    console.error('OnlyOffice错误:', errorCode, errorDescription);
    
    if (errorCode === -2) {
        message.error('文档服务器连接失败，请检查服务器地址');
    } else if (errorCode === -3) {
        message.error('文档安全令牌错误，请联系管理员');
    } else {
        message.error(`文档编辑器错误: ${errorDescription}`);
    }
};
// 测试页码跳转功能
const testJumpPage = async (page: number) => {
    if (!docEditorInstance.value) {
        console.error('文档编辑器实例不可用');
        message.error('文档编辑器未准备就绪');
        return;
    }

    console.log(`=== 跳转到第${page}页 ===`);
    console.log('文档编辑器实例:', docEditorInstance.value);
    console.log('连接器实例:', connector.value);

    try {
        // 方法1: 尝试使用 executeMethod
        if (typeof docEditorInstance.value.executeMethod === 'function') {
            console.log('executeMethod 方法可用，尝试调用...');

            const result = await new Promise((resolve) => {
                docEditorInstance.value.executeMethod('PasteText', [`测试跳转到第${page}页\n`], (res: any) => {
                    console.log('executeMethod 结果:', res);
                    resolve(res);
                });
            });

            message.success(`executeMethod 调用成功，结果: ${result}`);
            return;
        }

        // 方法2: 尝试使用连接器
        if (connector.value) {
            console.log('尝试使用连接器...');
            console.log('连接器可用方法:', Object.getOwnPropertyNames(connector.value));

            // 尝试调用连接器的方法
            if (typeof connector.value.executeMethod === 'function') {
                const result = await connector.value.executeMethod('PasteText', [`连接器测试跳转到第${page}页\n`]);
                console.log('连接器 executeMethod 结果:', result);
                message.success(`连接器调用成功`);
                return;
            }
        }

        // 方法3: 直接尝试调用可能存在的方法
        const possibleMethods = ['goToPage', 'setPage', 'jumpToPage', 'navigateToPage'];
        for (const methodName of possibleMethods) {
            if (typeof docEditorInstance.value[methodName] === 'function') {
                console.log(`找到方法: ${methodName}`);
                try {
                    const result = docEditorInstance.value[methodName](page);
                    console.log(`${methodName} 调用结果:`, result);
                    message.success(`使用 ${methodName} 跳转成功`);
                    return;
                } catch (error) {
                    console.log(`${methodName} 调用失败:`, error);
                }
            }
        }

        // 如果所有方法都不可用
        console.error('所有页码跳转方法都不可用');
        console.log('文档编辑器实例的所有属性和方法:');
        console.log(Object.getOwnPropertyNames(docEditorInstance.value));
        console.log(Object.getOwnPropertyDescriptors(docEditorInstance.value));

        message.warning(`页码跳转功能不可用，但已记录到第${page}页的跳转请求`);

    } catch (error) {
        console.error('页码跳转失败:', error);
        message.error('页码跳转失败');
    }
};

// 测试高亮功能
const testHighlight = async (data: any) => {
    if (!docEditorInstance.value) {
        console.error('文档编辑器实例不可用');
        message.error('文档编辑器未准备就绪');
        return;
    }

    console.log(`=== 跳转到第${data.page}页并高亮选区 ===`);
    console.log('高亮数据:', data);

    try {
        // 先跳转到指定页码
        await testJumpPage(data.page);

        // 等待一下让页面跳转完成
        await new Promise(resolve => setTimeout(resolve, 1000));

        // 记录高亮测试信息
        console.log('开始高亮测试...');
        console.log('目标页码:', data.page);
        console.log('高亮位置:', data.position);

        // 处理高亮选区
        if (data.position && data.position.length > 0) {
            for (let i = 0; i < data.position.length; i++) {
                const pos = data.position[i];
                console.log(`处理第${i + 1}个高亮区域:`, pos);
                console.log(`坐标: (${pos.x1}, ${pos.y1}) → (${pos.x2}, ${pos.y2})`);
                console.log(`区域大小: ${pos.x2 - pos.x1} × ${pos.y2 - pos.y1}`);

                // 尝试高亮这个区域
                await highlightRegion(pos, i + 1);
            }
        }

        message.success(`高亮测试完成 - 页码: ${data.page}, 区域数: ${data.position ? data.position.length : 0}`);
    } catch (error) {
        console.error('高亮功能失败:', error);
        message.error('高亮功能失败');
    }
};

// 高亮指定区域
const highlightRegion = async (position: any, index: number = 1) => {
    console.log(`=== 高亮第${position.page}页的区域 #${index} ===`);
    console.log('坐标:', position);

    // 记录高亮区域信息
    const regionInfo = {
        page: position.page,
        coordinates: `(${position.x1}, ${position.y1}) → (${position.x2}, ${position.y2})`,
        size: `${position.x2 - position.x1} × ${position.y2 - position.y1}`,
        area: (position.x2 - position.x1) * (position.y2 - position.y1),
        index: index
    };

    console.log('高亮区域信息:', regionInfo);

    // 模拟高亮效果（在实际应用中，这里会调用真正的高亮API）
    console.log(`✅ 模拟高亮区域 #${index} 完成`);
    console.log(`   页码: ${position.page}`);
    console.log(`   坐标: (${position.x1}, ${position.y1}) → (${position.x2}, ${position.y2})`);
    console.log(`   面积: ${regionInfo.area} 像素²`);

    // 在实际的 OnlyOffice 环境中，这里应该调用相应的 API
    // 例如：docEditorInstance.value.selectRegion(position.x1, position.y1, position.x2, position.y2)
    //      docEditorInstance.value.setHighlightColor('#ff0000')

    return regionInfo;
};

// 安全执行 executeMethod 的辅助方法
const executeMethodSafely = async (methodName: string, parameters: any[] = []) => {
    return new Promise((resolve) => {
        try {
            if (docEditorInstance.value && typeof docEditorInstance.value.executeMethod === 'function') {
                docEditorInstance.value.executeMethod(methodName, parameters, (result: any) => {
                    console.log(`${methodName} 执行结果:`, result);
                    resolve(result);
                });
            } else {
                console.warn(`executeMethod 不可用或 ${methodName} 方法不存在`);
                resolve(false);
            }
        } catch (error) {
            console.error(`执行 ${methodName} 方法时出错:`, error);
            resolve(false);
        }
    });
};
// JWT令牌生成
const setupJWT = () => {
    const payload = {
        document: config.document,
        documentType: config.documentType,
        editorConfig: config.editorConfig,
        height: "100%",
        width: "100%"
    };
    
    // 使用与docs/index.html相同的密钥
    const secret = 'jwt@bos6688';
    config.token = createJWT(payload, secret);
};

/**
 * 生成 ONLYOFFICE 需要的 JWT（HS256）
 * @param {object} payload  ONLYOFFICE 配置对象
 * @param {string} secret   与 ONLYOFFICE 服务端一致的密钥
 * @returns {string} 标准 JWT
 */
const createJWT = (payload: any, secret: string): string => {
    if (!secret) return '';

    const header = { alg: 'HS256', typ: 'JWT' };
    const headerEnc = base64url(JSON.stringify(header));
    const payloadEnc = base64url(JSON.stringify(payload));

    const signature = CryptoJS.HmacSHA256(`${headerEnc}.${payloadEnc}`, secret);
    const signatureEnc = signature.toString(CryptoJS.enc.Base64)
        .replace(/\+/g, '-')
        .replace(/\//g, '_')
        .replace(/=/g, '');

    return `${headerEnc}.${payloadEnc}.${signatureEnc}`;
};

/**
 * Base64URL编码
 * @param {string} data 要编码的数据
 * @returns {string} Base64URL编码后的字符串
 */
const base64url = (data: string): string => {
    // 使用CryptoJS进行Base64编码
    const base64 = CryptoJS.enc.Base64.stringify(
        CryptoJS.enc.Utf8.parse(data)
    );
    
    // 转换为Base64URL格式
    return base64
        .replace(/\+/g, '-')
        .replace(/\//g, '_')
        .replace(/=/g, '');
}; 

onMounted(async () => {
    // 生成JWT令牌
    setupJWT(); 
    nextTick(() => {
        isComponentReady.value = true;
    });
});

// 组件卸载时清理监听器
onUnmounted(() => { 
});
</script>

<style scoped>
.document-editor-wrapper {
    display: flex;
    width: 100%;
    height: 100vh;
}

.document-editor-container {
    flex: 1;
    height: 100vh;
    position: relative;
    border-right: 1px solid #e8e8e8;
}

.loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    font-size: 16px;
    color: #666;
}

#docEditor {
    width: 100%;
    height: 100%;
}

.suggestion-panel {
    width: 350px;
    height: 100vh;
    overflow-y: auto;
    background-color: #f9f9f9;
    padding: 16px;
    box-sizing: border-box;
}

.suggestion-panel h3 {
    margin-top: 0;
    margin-bottom: 16px;
    font-size: 18px;
    color: #333;
    border-bottom: 1px solid #e8e8e8;
    padding-bottom: 8px;
} 

.suggestion-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.suggestion-item {
    background-color: white;
    border-radius: 4px;
    padding: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    cursor: pointer;
    transition: all 0.3s;
    border: 1px solid #e8e8e8;
}

.suggestion-item:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.suggestion-item.active {
    border-color: #1890ff;
    background-color: #e6f7ff;
}

.suggestion-content {
    margin-bottom: 12px;
}

.suggestion-title {
    font-weight: bold;
    margin-bottom: 8px;
    color: #333;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.suggestion-status {
    font-size: 12px;
    padding: 2px 8px;
    border-radius: 12px;
    font-weight: normal;
}

.suggestion-status.pending {
    background-color: #fff7e6;
    color: #fa8c16;
    border: 1px solid #ffd591;
}

.suggestion-status.accepted {
    background-color: #f6ffed;
    color: #52c41a;
    border: 1px solid #b7eb8f;
}

.suggestion-status.rejected {
    background-color: #fff2f0;
    color: #ff4d4f;
    border: 1px solid #ffccc7;
}

.suggestion-text {
    font-size: 14px;
    color: #666;
}

.original-text, .suggested-text {
    margin-bottom: 4px;
    word-break: break-all;
}

.original-text {
    color: #ff4d4f;
    text-decoration: line-through;
}

.suggested-text {
    color: #52c41a;
}

.location {
    font-size: 12px;
    color: #999;
    margin-top: 8px;
    line-height: 1.4;
}

.location > div {
    margin-bottom: 2px;
}

.suggestion-actions {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
}

.accept-btn, .reject-btn {
    padding: 4px 12px;
    border-radius: 4px;
    border: none;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s;
}

.accept-btn {
    background-color: #52c41a;
    color: white;
}

.accept-btn:hover {
    background-color: #73d13d;
}

.reject-btn {
    background-color: #f5f5f5;
    color: #666;
}

.reject-btn:hover {
    background-color: #e8e8e8;
}
</style>

