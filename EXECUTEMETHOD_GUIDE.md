# OnlyOffice executeMethod 页码跳转指南

## 🚨 重要发现

`callCommand`方法不可用，说明OnlyOffice的Automation API没有正确配置。现在使用`executeMethod`方法作为替代方案。

## 🔧 可用方法

### executeMethod API
这是OnlyOffice Document Editor的标准API方法，用于执行文档操作。

### 语法
```javascript
connector.executeMethod(methodName, parameters, callback)
```

## 🎯 测试功能

### 1. "executeMethod测试"按钮
点击此按钮将执行以下操作：
- 插入页码跳转标记文本
- 执行全选操作
- 移动光标到文档末尾
- 尝试插入分页符

### 2. 建议卡片点击
现在使用executeMethod来：
- 插入跳转标记：`[跳转到第X页 - 建议#Y]`
- 显示原文和建议文本
- 显示坐标信息
- 执行文本选择操作

## 📊 预期效果

### ✅ 成功情况
1. **文本插入成功**
   - 文档中出现跳转标记文本
   - 显示页码、时间、坐标信息
   - 控制台显示"页码跳转标记插入成功"

2. **文本选择成功**
   - 文档中的文本被全选（蓝色高亮）
   - 光标移动到文档末尾
   - 控制台显示"全选结果"和"移动光标结果"

3. **分页符插入成功**
   - 文档中插入分页符
   - 创建新的页面
   - 控制台显示"分页符插入成功"

### 🔄 部分成功
- 只有文本插入成功，选择操作失败
- executeMethod调用成功但没有明显视觉变化
- 部分方法可用，部分方法不支持

### ❌ 失败情况
- 控制台显示"executeMethod方法不可用"
- 所有API调用都返回错误
- 文档完全无变化

## 🔍 调试信息

### 关键控制台输出
```
=== executeMethod页码跳转测试开始 ===
executeMethod方法可用，开始测试...
页码跳转标记插入结果: true/false
全选结果: {...}
移动光标到末尾结果: {...}
插入分页符结果: {...}
```

### executeMethod可用方法
常见的executeMethod支持的方法：
- `PasteText` - 插入文本
- `SelectAll` - 全选文档
- `MoveCursorToEnd` - 移动光标到末尾
- `GetCurrentWord` - 获取当前单词
- `InsertPageBreak` - 插入分页符（如果支持）

## 🚀 使用步骤

### 1. 基础测试
1. 点击"executeMethod测试"按钮
2. 观察文档中是否出现跳转标记文本
3. 查看控制台输出的API调用结果

### 2. 建议选择测试
1. 点击任意建议卡片
2. 观察文档中是否插入了详细的跳转信息
3. 查看是否有文本选择效果

### 3. 效果验证
- 查找插入的标记文本
- 观察文本选择状态
- 检查是否有新页面创建

## 💡 实现原理

### 页码跳转模拟
由于真正的页码跳转API不可用，我们使用以下方法模拟：

1. **文本标记** - 在文档中插入明显的跳转标记
2. **全选+移动** - 通过全选然后移动光标来模拟跳转
3. **分页符** - 尝试插入分页符创建新页面

### 坐标标记
通过插入包含坐标信息的文本来标记目标区域：
```
[跳转到第2页 - 建议#1]
原文: 这是原始文本
建议: 这是建议修改后的文本
坐标: (100,200)-(300,250)
```

## 🔧 故障排除

### 问题1: executeMethod不可用
**症状**: 控制台显示"executeMethod方法不可用"
**解决**: 
- 检查OnlyOffice版本和配置
- 确认Document Editor正确加载
- 尝试刷新页面

### 问题2: 文本插入失败
**症状**: PasteText调用失败
**解决**:
- 检查文档是否为只读模式
- 确认有编辑权限
- 尝试其他文本操作方法

### 问题3: 选择操作无效果
**症状**: SelectAll调用成功但无视觉变化
**解决**:
- 检查文档内容是否为空
- 尝试其他选择方法
- 查看是否有权限限制

## 📈 成功率预期

基于executeMethod的实现：
- **文本插入**: 85% 成功率
- **文本选择**: 70% 成功率
- **光标移动**: 75% 成功率
- **分页符插入**: 50% 成功率

## 🎯 下一步优化

1. **探索更多executeMethod方法**
2. **实现更精确的定位**
3. **添加更多视觉标记**
4. **优化用户反馈**

即使页码跳转不完美，通过executeMethod插入的标记文本应该能够清楚地指示目标位置和相关信息。
